# Migration Guide: From Custom Split to Unleash

This document describes the migration from the custom murmur3-based A/B testing to Unleash feature flags.

## What Changed

### Removed Components
- **murmur3 hashing logic** - No longer needed for user assignment
- **SPLIT_PERCENT environment variable** - Percentage is now controlled in Unleash
- **Custom determineGroup function** - Replaced with Unleash client calls

### Added Components
- **Unleash Go client** - Official Unleash client for Go
- **New environment variables** for Unleash configuration
- **Unleash context** - Proper user context for feature flag evaluation

## Environment Variables Changes

### Removed
```bash
SPLIT_PERCENT=50  # No longer needed
```

### Added
```bash
UNLEASH_URL=https://your-unleash-instance.com/api
UNLEASH_API_KEY=your-api-key-here
UNLEASH_APP_NAME=splitter
UNLEASH_FEATURE=monolit-mf-ab-test
```

## Unleash Setup Required

1. **Install Unleash** (if not already available)
   - Use hosted Unleash or deploy your own instance
   - See docker-compose.yml for local development setup

2. **Create Feature Flag**
   - Name: `monolit-mf-ab-test` (or set custom name in `UNLEASH_FEATURE`)
   - Type: Release toggle or Experiment
   - Configure targeting rules (percentage, user segments, etc.)

3. **Generate API Token**
   - Create a client API token in Unleash admin
   - Set it in `UNLEASH_API_KEY` environment variable

## Migration Steps

### 1. Deploy Unleash (if needed)
```bash
# For local development
docker-compose up -d postgres unleash

# Wait for Unleash to be ready
curl http://localhost:4242/health
```

### 2. Configure Feature Flag
1. Open Unleash admin UI (http://localhost:4242 for local)
2. Create new feature flag: `monolit-mf-ab-test`
3. Configure percentage rollout or other targeting rules
4. Enable the feature flag

### 3. Update Environment Variables
```bash
# Update your .env file or Kubernetes secrets
UNLEASH_URL=http://localhost:4242/api  # or your Unleash URL
UNLEASH_API_KEY=default:development.unleash-insecure-api-token
```

### 4. Deploy Updated Service
```bash
# Build and deploy
make docker-build
docker-compose up -d splitter
```

### 5. Verify Migration
```bash
# Check health
curl http://localhost:8080/health

# Test A/B assignment
curl -v http://localhost:8080/
```

## Benefits of Migration

### Before (Custom Split)
- ❌ Hard-coded percentage in environment
- ❌ No real-time changes without restart
- ❌ Limited targeting capabilities
- ❌ No centralized experiment management
- ❌ Manual hash calculation

### After (Unleash)
- ✅ Dynamic percentage control from UI
- ✅ Real-time experiment changes
- ✅ Advanced targeting (user segments, gradual rollout)
- ✅ Centralized experiment dashboard
- ✅ Built-in metrics and analytics
- ✅ A/B test variants support

## Rollback Plan

If you need to rollback to the custom split logic:

1. **Revert code changes**
   ```bash
   git revert <commit-hash>
   ```

2. **Restore environment variables**
   ```bash
   SPLIT_PERCENT=50
   # Remove UNLEASH_* variables
   ```

3. **Redeploy service**
   ```bash
   make docker-build
   docker-compose up -d splitter
   ```

## Monitoring

After migration, monitor:

1. **Service health** - `/health` endpoint
2. **Unleash connectivity** - Check logs for Unleash errors
3. **Experiment metrics** - Use Unleash dashboard
4. **Analytics events** - Verify events are still sent to SOGU

## Troubleshooting

### Common Issues

1. **Unleash connection failed**
   - Check `UNLEASH_URL` is correct
   - Verify `UNLEASH_API_KEY` is valid
   - Ensure network connectivity

2. **Feature flag not found**
   - Verify feature flag exists in Unleash
   - Check `UNLEASH_FEATURE` environment variable
   - Ensure feature flag is enabled

3. **All users in same group**
   - Check feature flag targeting rules
   - Verify percentage rollout configuration
   - Review Unleash logs for evaluation details

### Debug Commands

```bash
# Check service logs
docker-compose logs splitter

# Test Unleash API directly
curl -H "Authorization: YOUR_API_KEY" \
     http://localhost:4242/api/client/features

# Verify feature flag evaluation
curl -H "Authorization: YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"userId": "test-user"}' \
     http://localhost:4242/api/client/features/monolit-mf-ab-test
```
