# Splitter Service

A/B testing proxy service that uses Unleash for feature flag management.

## Overview

This service acts as a proxy between users and your application, performing A/B testing based on Unleash feature flags. Users are assigned to different groups and can be redirected to different URLs or proxied to different services based on the feature flag configuration.

## Features

- **Unleash Integration**: Uses Unleash client for feature flag management
- **Device ID Tracking**: Assigns persistent device IDs via cookies
- **Analytics**: Sends experiment events to analytics service
- **Health Checks**: Built-in health check endpoint
- **Graceful Shutdown**: Proper cleanup on shutdown
- **Structured Logging**: JSON-formatted logs

## Configuration

The service is configured via environment variables:

### Required Variables

- `UNLEASH_URL`: URL of your Unleash instance (e.g., `https://your-unleash.com/api`)
- `UNLEASH_API_KEY`: API key for Unleash authentication

### Optional Variables

- `UNLEASH_APP_NAME`: Application name for Unleash (default: `splitter`)
- `UNLEASH_FEATURE`: Feature flag name to check (default: `monolit-mf-ab-test`)
- `EXPERIMENT_NAME`: Name of the experiment (default: `monolit-mf-ab-test`)
- `EXPERIMENT_GROUP_A`: Name for control group (default: `Monolit`)
- `EXPERIMENT_GROUP_B`: Name for treatment group (default: `MF`)
- `EXPERIMENT_EVENT_HIT`: Analytics event name (default: `experiments.hit`)
- `REDIRECT_URL`: URL to redirect treatment group (default: `https://google.com`)
- `PROXY_URL`: URL to proxy control group (default: `https://ya.ru`)
- `SOGU_URL`: Analytics service URL
- `BROWSER_DEBUG_LOGS`: Enable debug logs (default: `false`)
- `PORT`: Server port (default: `8080`)

## How It Works

1. **Request Processing**: Each incoming request gets a device ID (from cookie or newly generated)
2. **Feature Flag Check**: The service checks Unleash to determine if the feature is enabled for this user
3. **Group Assignment**: 
   - If feature is enabled → User goes to Group B (treatment)
   - If feature is disabled → User goes to Group A (control)
4. **Action**: 
   - Group B users are redirected to `REDIRECT_URL`
   - Group A users are proxied to `PROXY_URL`
5. **Analytics**: Experiment events are sent asynchronously to the analytics service

## Unleash Setup

1. Create a feature flag in your Unleash instance
2. Configure targeting rules (e.g., percentage rollout, user segments)
3. Set the feature flag name in `UNLEASH_FEATURE` environment variable

## Building and Running

```bash
# Build
go build -o splitter

# Run
./splitter
```

## Docker

```bash
# Build image
docker build -t splitter .

# Run container
docker run -p 8080:8080 --env-file .env splitter
```

## Health Check

The service provides a health check endpoint at `/health`:

```bash
curl http://localhost:8080/health
```

## Analytics Events

The service sends analytics events with the following structure:

```json
{
  "app_version": "1.0.0",
  "event_name": "experiments.hit",
  "platform": "web",
  "url": "/path",
  "device_id": "uuid",
  "user_agent": "Mozilla/5.0...",
  "dt": **********,
  "params": {
    "device_id": "uuid",
    "experiment": "monolit-mf-ab-test",
    "variant": "MF",
    "was_generated": false
  }
}
```

## Migration from Custom Split Logic

This version replaces the previous murmur3-based splitting logic with Unleash feature flags. The main benefits:

- **Centralized Control**: Manage experiments from Unleash dashboard
- **Advanced Targeting**: Use Unleash's powerful targeting capabilities
- **Real-time Changes**: Update experiment parameters without redeployment
- **Better Analytics**: Integration with Unleash metrics
