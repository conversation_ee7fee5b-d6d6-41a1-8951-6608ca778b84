version: '3.8'

services:
  # Unleash database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: unleash
      POSTGRES_USER: unleash
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U unleash"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Unleash server
  unleash:
    image: unleashorg/unleash-server:latest
    ports:
      - "4242:4242"
    environment:
      DATABASE_URL: *****************************************/unleash
      DATABASE_SSL: "false"
      LOG_LEVEL: warn
      INIT_FRONTEND_API_TOKENS: default:development.unleash-insecure-frontend-api-token
      INIT_CLIENT_API_TOKENS: default:development.unleash-insecure-api-token
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4242/health"]
      interval: 1s
      timeout: 1s
      retries: 5
      start_period: 15s

  # Splitter service
  splitter:
    build: .
    ports:
      - "8080:8080"
    environment:
      UNLEASH_URL: http://unleash:4242/api
      UNLEASH_API_KEY: default:development.unleash-insecure-api-token
      UNLEASH_APP_NAME: splitter
      UNLEASH_FEATURE: monolit-mf-ab-test
      EXPERIMENT_NAME: monolit-mf-ab-test
      EXPERIMENT_GROUP_A: Monolit
      EXPERIMENT_GROUP_B: MF
      REDIRECT_URL: https://google.com
      PROXY_URL: https://ya.ru
      PORT: "8080"
    depends_on:
      unleash:
        condition: service_healthy

volumes:
  postgres_data:
