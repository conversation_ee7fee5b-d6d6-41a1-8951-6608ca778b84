apiVersion: v1
kind: ConfigMap
metadata:
  name: splitter-config
  namespace: default
data:
  EXPERIMENT_NAME: "monolit-mf-ab-test"
  EXPERIMENT_GROUP_A: "Monolit"
  EXPERIMENT_GROUP_B: "MF"
  EXPERIMENT_EVENT_HIT: "experiments.hit"
  REDIRECT_URL: "https://google.com"
  PROXY_URL: "https://ya.ru"
  BROWSER_DEBUG_LOGS: "false"
  UNLEASH_APP_NAME: "splitter"
  UNLEASH_FEATURE: "monolit-mf-ab-test"
  PORT: "8080"

---
apiVersion: v1
kind: Secret
metadata:
  name: splitter-secrets
  namespace: default
type: Opaque
stringData:
  UNLEASH_URL: "https://your-unleash-instance.com/api"
  UNLEASH_API_KEY: "your-api-key-here"
  SOGU_URL: "https://sogu-staging.sogu.dev.tripster.tech/events/"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: splitter
  namespace: default
  labels:
    app: splitter
spec:
  replicas: 3
  selector:
    matchLabels:
      app: splitter
  template:
    metadata:
      labels:
        app: splitter
    spec:
      containers:
      - name: splitter
        image: splitter:latest
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: splitter-config
        - secretRef:
            name: splitter-secrets
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "64Mi"
            cpu: "250m"
          limits:
            memory: "128Mi"
            cpu: "500m"

---
apiVersion: v1
kind: Service
metadata:
  name: splitter-service
  namespace: default
spec:
  selector:
    app: splitter
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: splitter-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: splitter.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: splitter-service
            port:
              number: 80
