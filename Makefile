.PHONY: build run test clean docker-build docker-run docker-compose-up docker-compose-down

# Variables
APP_NAME=splitter
DOCKER_IMAGE=splitter:latest

# Build the application
build:
	go build -o $(APP_NAME) .

# Run the application locally
run: build
	./$(APP_NAME)

# Run tests
test:
	go test -v ./...

# Clean build artifacts
clean:
	rm -f $(APP_NAME)
	go clean

# Build Docker image
docker-build:
	docker build -t $(DOCKER_IMAGE) .

# Run Docker container
docker-run: docker-build
	docker run -p 8080:8080 --env-file .env $(DOCKER_IMAGE)

# Start services with docker-compose
docker-compose-up:
	docker-compose up -d

# Stop services with docker-compose
docker-compose-down:
	docker-compose down

# View logs
logs:
	docker-compose logs -f splitter

# Format code
fmt:
	go fmt ./...

# Lint code
lint:
	golangci-lint run

# Download dependencies
deps:
	go mod download
	go mod tidy

# Build for production
build-prod:
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -ldflags '-w -s' -o $(APP_NAME) .

# Help
help:
	@echo "Available targets:"
	@echo "  build          - Build the application"
	@echo "  run            - Run the application locally"
	@echo "  test           - Run tests"
	@echo "  clean          - Clean build artifacts"
	@echo "  docker-build   - Build Docker image"
	@echo "  docker-run     - Run Docker container"
	@echo "  docker-compose-up   - Start services with docker-compose"
	@echo "  docker-compose-down - Stop services with docker-compose"
	@echo "  logs           - View application logs"
	@echo "  fmt            - Format code"
	@echo "  lint           - Lint code"
	@echo "  deps           - Download and tidy dependencies"
	@echo "  build-prod     - Build for production"
	@echo "  help           - Show this help"
